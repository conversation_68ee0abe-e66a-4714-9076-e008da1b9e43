@import '../../../../global';

.hidden {
    display: none !important;
}

.loadingSpinner {
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 100%;
}

.modalContent {
    position: relative;
    min-height: 200px;

    .introText {
        color: $secondaryColor;
        margin-bottom: 20px;
        text-align: center;
        line-height: 24px;
    }

    // Dropdown styling to match project standards
    .dropdown {
        width: 100%;
        margin: 0 auto;

        // Override JC_Dropdown styles to fit in our layout
        :global(.mainContainer) {
            width: 100%;

            :global(.mainButton) {
                background-color: $offWhite;

                :global(.optionLabel) {
                    color: $offBlack;
                }
            }

            :global(.dropdown) {
                :global(.dropdownOption) {
                    :global(.optionLabel) {
                        color: $offBlack;
                    }
                }
            }
        }
    }

    .productsTableContainer {
        max-height: 400px;
        width: max-content;
        overflow-y: auto;
        margin: -20px auto 15px auto;

        .productsTable {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0 15px;

            .productRow {

                .productCell {
                    text-align: center;
                    padding: 15px;
                    border-radius: $tinyBorderRadius;

                    .productContainer {
                        width: 100%;

                        .productInfo {
                            margin-bottom: 15px;

                            .productName {
                                font-weight: bold;
                                color: $secondaryColor;
                                font-size: 1.1em;
                                margin-bottom: 5px;
                            }

                            .productDetails {
                                margin-top: 10px;
                                color: rgba($secondaryColor, 0.8);
                                font-size: 0.9em;
                            }
                        }

                        .methodSelection {
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            width: 100%;

                            .currentMethodCell {
                                width: 145px;
                                overflow: visible;
                                color: $secondaryColor;
                                text-align: right;
                                background-color: rgba($offBlack, 0.3);
                                border-radius: $tinyBorderRadius;
                                white-space: nowrap;
                                direction: rtl;
                            }

                            .arrowCell {
                                width: 80px;
                                display: flex;
                                justify-content: center;
                                align-items: center;

                                .arrow {
                                    filter: invert(100%) sepia(0%) saturate(0%) hue-rotate(93deg) brightness(103%) contrast(103%);
                                    transform: rotate(-90deg); // Point right instead of down
                                }
                            }

                            .newMethodCell {
                                flex: 1;
                                min-width: 120px;
                                max-width: 200px;
                            }
                        }
                    }
                }
            }
        }
    }

    .buttonsContainer {
        margin-top: 14px;
        display: flex;
        justify-content: center;
        column-gap: 12%;
    }
}
